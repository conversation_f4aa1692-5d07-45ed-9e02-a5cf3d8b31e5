-- 确保 RaidCalendar 表存在
if not RaidCalendar then
    RaidCalendar = {}
end

-- 立即定义主函数（创建固定的3个面板，不重复创建）
function RaidCalendar_CreateProfessionPanel(parentFrame)
    if not parentFrame then return end
    
    -- 只创建一次，不重复创建
    if parentFrame.professionPanelCreated then
        return
    end
    parentFrame.professionPanelCreated = true
    
    -- 检测已学专业
    local learned = RaidCalendar_AutoDetectProfessions()

    -- 检查是否有可用的专业技能（包括同步来的）
    local hasAnyAvailableProfession = false
    local professions = {"炼金术", "裁缝", "制皮"}
    
    for _, profKey in ipairs(professions) do
        -- 自己学会了
        if learned[profKey] then
            hasAnyAvailableProfession = true
            break
        end
        
        -- 或者同步数据中有人学会了且可用
        if RaidCalendarCharacterDB and RaidCalendarCharacterDB.syncedProfessions then
            for charName, charData in pairs(RaidCalendarCharacterDB.syncedProfessions) do
                if charData.professionCDs then
                    for profession, data in pairs(charData.professionCDs) do
                        local isMatch = false
                        if profKey == "炼金术" and string.find(profession, "炼金") then
                            isMatch = true
                        elseif profKey == "裁缝" and string.find(profession, "裁缝") then
                            isMatch = true
                        elseif profKey == "制皮" and string.find(profession, "制皮") then
                            isMatch = true
                        end
                        
                        if isMatch then
                            -- 检查是否有可用状态
                            if data and data.items and data.lastUpdate then
                                for _, item in pairs(data.items) do
                                    if item and item.remaining then
                                        local remaining = math.max(0, item.remaining - (time() - data.lastUpdate))
                                        if remaining == 0 then
                                            hasAnyAvailableProfession = true
                                            break
                                        end
                                    end
                                end
                            end
                            if hasAnyAvailableProfession then break end
                        end
                    end
                    if hasAnyAvailableProfession then break end
                end
                if hasAnyAvailableProfession then break end
            end
            if hasAnyAvailableProfession then break end
        end
    end
    
    -- 始终创建标题和面板
    local title = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    title:SetPoint("TOP", parentFrame, "TOP", 95, -20)  -- 从75改为95，向右移动20像素像素
    title:SetText("专业技能冷却")
    title:SetTextColor(1, 1, 0)
    
    local playerName = UnitName("player")
    local _, classToken = UnitClass("player")
    local colorHex = RaidCalendar_GetClassColorHex(classToken)
    
    local yOffset = -110
    
    -- 专业技能数据（动态排序：已学的排在前面）
    local professionData = {
        {name = "炼金术-转化", key = "炼金术", icon = "Interface\\AddOns\\RaidCalendar\\textures\\LJS_Icon.tga"},
        {name = "裁缝-月布", key = "裁缝", icon = "Interface\\AddOns\\RaidCalendar\\textures\\CF_Icon.tga"},
        {name = "制皮-精炼", key = "制皮", icon = "Interface\\AddOns\\RaidCalendar\\textures\\ZP_Icon.tga"}
    }
    
    -- 动态排序：将已学的专业排在前面
    table.sort(professionData, function(a, b)
        local aLearned = learned[a.key] or false
        local bLearned = learned[b.key] or false
        
        if aLearned and not bLearned then
            return true  -- a已学，b未学，a排在前面
        elseif not aLearned and bLearned then
            return false -- a未学，b已学，b排在前面
        else
            -- 都学了或都没学，保持原有顺序（炼金术、裁缝、制皮）
            local order = {["炼金术"] = 1, ["裁缝"] = 2, ["制皮"] = 3}
            return (order[a.key] or 999) < (order[b.key] or 999)
        end
    end)
    
    -- 存储面板元素
    parentFrame.professionButtons = {}
    parentFrame.professionDetails = {}
    
    local previousElement = nil  -- 跟踪前一个元素（按钮或详情框）
    
    for _, prof in ipairs(professionData) do
        local hasLearned = learned[prof.key]
        
        -- 始终创建按钮（移除条件限制）
        local profButton = CreateFrame("Button", nil, parentFrame)
        
        -- 首次创建时就使用相对位置
        if not previousElement then
            profButton:SetPoint("TOP", parentFrame, "TOP", 95, yOffset)  -- 从75改为95，向右移动20像素像素
        else
            profButton:SetPoint("TOP", previousElement, "BOTTOM", 0, -5)
        end
        
        profButton:SetWidth(180)
        profButton:SetHeight(20)
        
        -- 背景颜色（根据冷却状态动态变化）
        local profBg = profButton:CreateTexture(nil, "BACKGROUND")
        profBg:SetAllPoints(profButton)
        
        -- 动态设置颜色
        local cooldown = 0
        if prof.key == "制皮" then
            -- 制皮专业从本地数据获取状态
            if RaidCalendarCharacterDB and RaidCalendarCharacterDB.professionCDs and RaidCalendarCharacterDB.professionCDs["制皮"] then
                local data = RaidCalendarCharacterDB.professionCDs["制皮"]
                if data.clickTime and data.cooldownDuration then
                    -- 计算剩余冷却时间
                    local now = time()
                    local elapsed = now - data.clickTime
                    cooldown = math.max(0, data.cooldownDuration - elapsed)
                end
            end
        else
            cooldown = RaidCalendar_GetSavedCooldown(prof.key)
        end

        if cooldown > 0 then
            profBg:SetTexture(0.8, 0.3, 0.3, 0.5)  -- 淡红色：有冷却
        else
            profBg:SetTexture(0.3, 0.8, 0.3, 0.5)  -- 淡绿色：可用
        end
        
        -- 保存背景引用，方便后续更新颜色
        profButton.profBg = profBg
        
        -- 边框
        profButton:SetBackdrop({
            edgeFile = "Interface\\Buttons\\WHITE8X8",
            edgeSize = 1,
        })
        profButton:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.3)
        profButton:SetBackdropColor(0, 0, 0, 0)
        
        -- 展开图标
        local expandIcon = profButton:CreateTexture(nil, "OVERLAY")
        expandIcon:SetPoint("LEFT", profButton, "LEFT", 5, 0)
        expandIcon:SetWidth(16)
        expandIcon:SetHeight(16)
        expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
        
        -- 专业图标
        local profIcon = profButton:CreateTexture(nil, "OVERLAY")
        profIcon:SetPoint("LEFT", profButton, "LEFT", 25, 0)
        profIcon:SetWidth(20)
        profIcon:SetHeight(20)
        profIcon:SetTexture(prof.icon)  -- 这里使用了上面定义的图标路径
        
        -- 专业名称
        local profText = profButton:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
        profText:SetPoint("LEFT", profButton, "LEFT", 50, 0)
        profText:SetText(prof.name .. " (1人)")
        
        -- 创建角色详情框（动态位置，默认隐藏）
        local detailFrame = CreateFrame("Frame", nil, parentFrame)
        detailFrame:SetPoint("TOP", profButton, "BOTTOM", 0, -7)  -- 参考副本记录的间距 -7
        detailFrame:SetWidth(180)
        detailFrame:SetHeight(22)
        detailFrame:Hide() -- 默认隐藏
        
        local charText = detailFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
        charText:SetPoint("TOPLEFT", detailFrame, "TOPLEFT", 50, 0)  -- 保持30不变，因为这是相对于detailFrame的位置
        
        -- 获取保存的冷却时间并显示
        local status = cooldown > 0 and RaidCalendar_FormatCooldownTime(cooldown) or "可"  -- 从"可用"改为"可"
        charText:SetText("|cFF" .. colorHex .. playerName .. "|r - " .. status)
        
        -- 保存文本对象引用，方便后续更新
        detailFrame.charText = charText
        
        -- 简化点击事件（只管展开/收起）
        profButton:SetScript("OnClick", function()
            local wasVisible = detailFrame:IsVisible()
            
            -- 隐藏所有详情
            for _, otherDetail in pairs(parentFrame.professionDetails) do
                otherDetail:Hide()
            end
            
            -- 重置所有展开图标
            for _, otherButton in pairs(parentFrame.professionButtons) do
                if otherButton.expandIcon then
                    otherButton.expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
                end
            end
            
            -- 如果之前是隐藏的，现在显示
            if not wasVisible then
                detailFrame:Show()
                expandIcon:SetTexture("Interface\\Buttons\\UI-MinusButton-Up")
                -- 刷新数据
                RaidCalendar_RefreshProfessionData(parentFrame)
            end
            
            -- 重新调整按钮位置
            RaidCalendar_RepositionProfessionButtons(parentFrame)
        end)
        profButton:EnableMouse(true)
        profButton.expandIcon = expandIcon
        
        -- 存储引用
        parentFrame.professionButtons[prof.key] = profButton
        parentFrame.professionDetails[prof.key] = detailFrame
        
        -- 更新前一个元素为当前按钮
        previousElement = profButton
    end
    
    -- 创建后立即刷新，决定显示/隐藏
    RaidCalendar_RefreshProfessionData(parentFrame)
end

-- 检测已学专业（只检测裁缝、炼金术、制皮）
function RaidCalendar_AutoDetectProfessions()
    local learned = {}
    for i = 1, 500 do
        local spellName = GetSpellName(i, BOOKTYPE_SPELL)
        if not spellName then break end
        
        if string.find(spellName, "裁缝") then 
            learned["裁缝"] = true
        elseif string.find(spellName, "炼金") then 
            learned["炼金术"] = true  
        elseif string.find(spellName, "制皮") then 
            learned["制皮"] = true 
        end
    end
    return learned
end

-- 获取职业颜色
function RaidCalendar_GetClassColorHex(classToken)
    local colors = {
        ["WARRIOR"] = "C79C6E", ["PALADIN"] = "F58CBA", ["HUNTER"] = "ABD473",
        ["ROGUE"] = "FFF569", ["PRIEST"] = "FFFFFF", ["SHAMAN"] = "0070DE", 
        ["MAGE"] = "69CCF0", ["WARLOCK"] = "9482C9", ["DRUID"] = "FF7D0A"
    }
    return colors[classToken] or "FFFFFF"
end

-- 添加更新函数（仿照副本记录的更新方式）
function RaidCalendar_UpdateProfessionPanel(parentFrame)
    -- 直接调用创建函数，但要确保不会触发事件循环
    RaidCalendar_CreateProfessionPanel(parentFrame)
end

-- 函数别名
RaidCalendar_RefreshProfessionPanel = RaidCalendar_UpdateProfessionPanel

-- 添加重新定位函数（支持动态排序）
function RaidCalendar_RepositionProfessionButtons(parentFrame)
    if not parentFrame.professionButtons then return end
    
    -- 获取当前已学专业状态
    local learned = RaidCalendar_AutoDetectProfessions()
    
    -- 创建动态排序的专业列表
    local professionOrder = {
        {key = "炼金术", priority = learned["炼金术"] and 1 or 4},
        {key = "裁缝", priority = learned["裁缝"] and 2 or 5}, 
        {key = "制皮", priority = learned["制皮"] and 3 or 6}
    }
    
    -- 按优先级排序
    table.sort(professionOrder, function(a, b)
        return a.priority < b.priority
    end)
    
    local previousButton = nil
    
    for _, profInfo in ipairs(professionOrder) do
        local profKey = profInfo.key
        local button = parentFrame.professionButtons[profKey]
        local detail = parentFrame.professionDetails[profKey]
        
        if button and button:IsVisible() then
            -- 清除旧位置
            button:ClearAllPoints()
            
            -- 设置相对位置 - 与初始创建位置保持一致
            if not previousButton then
                button:SetPoint("TOP", parentFrame, "TOP", 95, -110)  -- 从75改为95，向右移动20像素像素
            else
                button:SetPoint("TOP", previousButton, "BOTTOM", 0, -5)
            end
            
            -- 如果详情框显示，需要调整下一个按钮的位置
            if detail and detail:IsVisible() then
                -- 详情框位置相对于按钮
                detail:ClearAllPoints()
                detail:SetPoint("TOP", button, "BOTTOM", 0, -7)
                
                -- 下一个按钮将相对于详情框定位
                previousButton = detail
            else
                -- 下一个按钮相对于当前按钮定位
                previousButton = button
            end
        end
    end
end

-- 格式化冷却时间显示
function RaidCalendar_FormatCooldownTime(seconds)
    if seconds <= 0 then return "可" end  -- 从"可用"改为"可"
    local days = math.floor(seconds / 86400)
    local hours = math.floor(math.mod(seconds, 86400) / 3600)
    local minutes = math.floor(math.mod(seconds, 3600) / 60)
    
    if days > 0 then 
        return days .. "天" .. hours .. "小时"
    elseif hours > 0 then 
        return hours .. "小时" .. minutes .. "分钟"
    else 
        return minutes .. "分钟" 
    end
end

-- 简化的冷却时间格式（修复格式一致性）
function RaidCalendar_FormatShortCooldownTime(seconds)
    if seconds <= 0 then return "" end
    
    local days = math.floor(seconds / 86400)
    local hours = math.floor(math.mod(seconds, 86400) / 3600)
    local minutes = math.floor(math.mod(seconds, 3600) / 60)
    
    -- 计算总小时数
    local totalHours = days * 24 + hours
    
    -- 统一使用4位格式：小时分钟（确保与解析规则匹配）
    if totalHours >= 100 then
        -- 超过99小时，只能显示99小时59分钟作为最大值
        return "9959"
    else
        -- 正常情况，使用4位格式
        return string.format("%02d%02d", totalHours, minutes)
    end
end

-- 获取专业冷却时间（GC插件逻辑：基于结束时间计算）
function RaidCalendar_GetSavedCooldown(profession)
    local playerName = UnitName("player")
    if not playerName or not RaidCalendarCharacterDB or not RaidCalendarCharacterDB.professionCDs then
        return 0
    end

    -- 首先检查同步数据中自己的专业信息
    if RaidCalendarCharacterDB.syncedProfessions and RaidCalendarCharacterDB.syncedProfessions[playerName] then
        local syncedData = RaidCalendarCharacterDB.syncedProfessions[playerName].professionCDs
        if syncedData and syncedData[profession] then
            local data = syncedData[profession]
            if data and data.items and data.lastUpdate then
                for _, item in ipairs(data.items) do
                    -- GC插件逻辑：使用结束时间计算剩余时间
                    if item and item.name and item.endTime then
                        local currentTime = time()
                        local remaining = math.max(0, item.endTime - currentTime)
                        return remaining
                    end
                end
            end
        end
    end

    -- 如果同步数据中没有，再检查本地专业数据（炼金术、裁缝）
    for savedProfession, data in pairs(RaidCalendarCharacterDB.professionCDs) do
        local isMatch = false

        -- 根据不同专业进行匹配
        if profession == "炼金术" and string.find(savedProfession, "炼金") then
            isMatch = true
        elseif profession == "裁缝" and string.find(savedProfession, "裁缝") then
            isMatch = true
        elseif profession == "制皮" and string.find(savedProfession, "制皮") then
            isMatch = true
        end

        if isMatch then
            -- GC插件逻辑：所有专业都使用统一的endTime格式
            if data and data.endTime then
                local currentTime = time()
                return math.max(0, data.endTime - currentTime)
            end
        end
    end

    return 0
end

-- 新增：获取专业技能冷却结束时间（GC插件逻辑）
function RaidCalendar_GetProfessionEndTime(profession)
    local playerName = UnitName("player")
    if not playerName or not RaidCalendarCharacterDB or not RaidCalendarCharacterDB.professionCDs then
        return nil
    end

    -- 首先检查同步数据中自己的专业信息
    if RaidCalendarCharacterDB.syncedProfessions and RaidCalendarCharacterDB.syncedProfessions[playerName] then
        local syncedData = RaidCalendarCharacterDB.syncedProfessions[playerName].professionCDs
        if syncedData and syncedData[profession] then
            local data = syncedData[profession]
            if data and data.items and data.lastUpdate then
                for _, item in ipairs(data.items) do
                    -- 优先使用结束时间
                    if item and item.name and item.endTime then
                        return item.endTime
                    end
                end
            end
        end
    end

    -- 检查本地专业数据
    for savedProfession, data in pairs(RaidCalendarCharacterDB.professionCDs) do
        local isMatch = false

        -- 根据不同专业进行匹配
        if profession == "炼金术" and string.find(savedProfession, "炼金") then
            isMatch = true
        elseif profession == "裁缝" and string.find(savedProfession, "裁缝") then
            isMatch = true
        elseif profession == "制皮" and string.find(savedProfession, "制皮") then
            isMatch = true
        end

        if isMatch then
            -- GC插件逻辑：所有专业都使用统一的endTime格式
            if data and data.endTime then
                return data.endTime
            end
        end
    end

    return nil
end

-- GC插件逻辑：物品冷却检测信息（仿照GroupCalendar）
local RaidCalendar_CooldownItemInfo = {
    [15846] = {EventID = "Leatherworking"}, -- Salt Shaker 筛盐器
    -- 可以添加其他物品，如雪人9000等
}

-- GC插件逻辑：检测背包中物品的冷却时间
function RaidCalendar_CheckItemCooldowns()
    for bagIndex = 0, NUM_BAG_SLOTS do
        local numBagSlots = GetContainerNumSlots(bagIndex)

        for bagSlotIndex = 1, numBagSlots do
            local itemLink = GetContainerItemLink(bagIndex, bagSlotIndex)

            if itemLink then
                -- 解析物品ID
                local _, _, _, itemCode = string.find(itemLink, "|%x+|Hitem:(%d+):")
                if itemCode then
                    itemCode = tonumber(itemCode)
                    local cooldownItemInfo = RaidCalendar_CooldownItemInfo[itemCode]

                    if cooldownItemInfo then
                        -- GC插件逻辑：使用GetContainerItemCooldown检测物品冷却
                        local start, duration, enable = GetContainerItemCooldown(bagIndex, bagSlotIndex)

                        if enable > 0 and duration > 0 then
                            local remainingTime = duration - (GetTime() - start)

                            if remainingTime > 0 then
                                -- 计算结束时间（GC插件逻辑）
                                local endTime = GetTime() + remainingTime

                                -- 根据物品类型保存冷却数据
                                if cooldownItemInfo.EventID == "Leatherworking" then
                                    -- 筛盐器冷却
                                    RaidCalendarCharacterDB.professionCDs["制皮"] = {
                                        endTime = endTime
                                    }

                                    -- 更新工会备注和界面
                                    RaidCalendar_UpdateGuildNote()
                                    if RaidCalendar.rightPanel then
                                        RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

-- GC插件逻辑：更新当前专业技能冷却（当专业技能面板打开时）
function RaidCalendar_UpdateCurrentTradeskillCooldown()
    local tradeskillName, currentLevel, maxLevel = GetTradeSkillLine()

    if not tradeskillName then
        return
    end

    -- 根据专业技能名称确定ID
    local tradeskillID = nil
    if string.find(tradeskillName, "炼金") then
        tradeskillID = "Alchemy"
    elseif string.find(tradeskillName, "裁缝") then
        tradeskillID = "Tailoring"
    end

    if tradeskillID then
        RaidCalendar_UpdateTradeskillCooldown(tradeskillID)
    end
end

-- GC插件逻辑：更新指定专业技能的冷却时间
function RaidCalendar_UpdateTradeskillCooldown(tradeskillID)
    local cooldown = RaidCalendar_GetTradeskillCooldown(tradeskillID)

    if cooldown and cooldown > 0 then
        -- 计算结束时间
        local endTime = time() + cooldown
        local profession = ""
        local skillName = ""

        if tradeskillID == "Alchemy" then
            profession = "炼金术"
            skillName = "转化：奥金"
        elseif tradeskillID == "Tailoring" then
            profession = "裁缝"
            skillName = "月布"
        end

        if profession ~= "" then
            -- GC插件逻辑：直接保存结束时间，不使用items数组
            RaidCalendarCharacterDB.professionCDs[profession] = {
                endTime = endTime
            }

            -- 更新工会备注和界面
            RaidCalendar_UpdateGuildNote()
            if RaidCalendar.rightPanel then
                RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
            end
        end
    end
end

-- GC插件逻辑：获取专业技能冷却时间（需要专业技能面板打开）
function RaidCalendar_GetTradeskillCooldown(tradeskillID)
    local numSkills = GetNumTradeSkills()
    local cooldownItems = {}

    -- 定义各专业的冷却技能
    if tradeskillID == "Alchemy" then
        cooldownItems = {"转化：奥金"}
    elseif tradeskillID == "Tailoring" then
        cooldownItems = {"月布"}
    else
        return nil
    end

    for skillIndex = 1, numSkills do
        local skillName, skillType = GetTradeSkillInfo(skillIndex)

        if skillName and skillType ~= "header" then
            -- 检查是否是冷却技能
            for _, cooldownItem in ipairs(cooldownItems) do
                if string.find(skillName, cooldownItem) then
                    local cooldown = GetTradeSkillCooldown(skillIndex)
                    return cooldown
                end
            end
        end
    end

    return nil
end

-- 添加职业颜色缓存
local classColorCache = {}

-- 获取角色职业颜色（移除调试信息）
function RaidCalendar_GetCharacterClassColor(charName)
    local playerName = UnitName("player")
    
    -- 如果是当前角色
    if charName == playerName then
        local _, classToken = UnitClass("player")
        return RaidCalendar_GetClassColorHex(classToken)
    end
    
    -- 检查缓存
    if classColorCache[charName] then
        return classColorCache[charName]
    end
    
    -- 从工会成员API查找（包括离线成员）
    for i = 1, GetNumGuildMembers() do
        local name, rank, rankIndex, level, class = GetGuildRosterInfo(i)
        if name == charName then
            if class then
                local classToken = RaidCalendar_ConvertClassNameToToken(class)
                if classToken then
                    local color = RaidCalendar_GetClassColorHex(classToken)
                    -- 缓存结果
                    classColorCache[charName] = color
                    return color
                end
            end
            break
        end
    end
    
    -- 从同步数据中查找（作为备用）
    if RaidCalendarCharacterDB and RaidCalendarCharacterDB.syncedProfessions and 
       RaidCalendarCharacterDB.syncedProfessions[charName] and 
       RaidCalendarCharacterDB.syncedProfessions[charName].classToken then
        local classToken = RaidCalendarCharacterDB.syncedProfessions[charName].classToken
        if classToken and classToken ~= "" then
            local color = RaidCalendar_GetClassColorHex(classToken)
            -- 缓存结果
            classColorCache[charName] = color
            return color
        end
    end
    
    -- 缓存默认颜色
    classColorCache[charName] = "FFFFFF"
    return "FFFFFF"
end

-- 添加职业名称到Token的转换函数
function RaidCalendar_ConvertClassNameToToken(className)
    local classMap = {
        ["战士"] = "WARRIOR",
        ["圣骑士"] = "PALADIN", 
        ["猎人"] = "HUNTER",
        ["盗贼"] = "ROGUE",
        ["牧师"] = "PRIEST",
        ["萨满祭司"] = "SHAMAN",
        ["法师"] = "MAGE",
        ["术士"] = "WARLOCK",
        ["德鲁伊"] = "DRUID",
        -- 英文名称
        ["Warrior"] = "WARRIOR",
        ["Paladin"] = "PALADIN",
        ["Hunter"] = "HUNTER", 
        ["Rogue"] = "ROGUE",
        ["Priest"] = "PRIEST",
        ["Shaman"] = "SHAMAN",
        ["Mage"] = "MAGE",
        ["Warlock"] = "WARLOCK",
        ["Druid"] = "DRUID"
    }
    return classMap[className]
end

-- 自动启用显示离线成员以获取完整工会信息
function RaidCalendar_EnsureOfflineMembersVisible()
    if not GetGuildRosterShowOffline() then
        SetGuildRosterShowOffline(1)
        -- 触发工会信息更新
        if IsInGuild() then
            GuildRoster()
        end
        return true -- 返回true表示刚刚启用了离线成员显示
    end
    return false -- 返回false表示已经启用了
end

-- 从工会备注同步专业技能信息（移除调试信息）
function RaidCalendar_SyncFromGuildNotes()
    if GetNumGuildMembers() <= 1 then return end
    
    -- 确保显示离线成员
    local justEnabled = RaidCalendar_EnsureOfflineMembersVisible()
    if justEnabled then
        -- 如果刚刚启用离线成员显示，延迟2秒后再同步，等待数据加载
        local delayFrame = CreateFrame("Frame")
        local delayTimer = 0
        delayFrame:SetScript("OnUpdate", function()
            delayTimer = delayTimer + arg1
            if delayTimer >= 2.0 then
                delayFrame:SetScript("OnUpdate", nil)
                RaidCalendar_SyncFromGuildNotes() -- 递归调用，但此时已经显示离线成员了
            end
        end)
        return
    end
    
    local syncedCount = 0
    local currentPlayerName = UnitName("player")
    
    -- 确保数据结构存在
    if not RaidCalendarCharacterDB then 
        RaidCalendarCharacterDB = {
            professionCDs = {},
            character = currentPlayerName
        }
    end
    if not RaidCalendarCharacterDB.syncedProfessions then
        RaidCalendarCharacterDB.syncedProfessions = {}
    end
    
    -- 遍历所有工会成员
    for i = 1, GetNumGuildMembers() do
        local name, rank, rankIndex, level, class, zone, publicNote, officerNote = GetGuildRosterInfo(i)
        
        if name and name ~= currentPlayerName then
            -- 预填充职业缓存
            if class and class ~= "" then
                local classToken = RaidCalendar_ConvertClassNameToToken(class)
                if classToken then
                    local color = RaidCalendar_GetClassColorHex(classToken)
                    classColorCache[name] = color
                end
            end
            
            -- 优先读取官员备注，如果没有则读取公开备注
            local noteToCheck = officerNote and officerNote ~= "" and officerNote or publicNote
            
            if noteToCheck and (string.find(noteToCheck, "月") or string.find(noteToCheck, "转") or string.find(noteToCheck, "盐")) then
                -- 解析简化格式：月2d3h,转16h,盐7200
                local professionData = RaidCalendar_ParseGuildNoteData(noteToCheck, 0)
                
                if professionData then
                    -- 获取职业token
                    local classToken = ""
                    if class and class ~= "" then
                        classToken = RaidCalendar_ConvertClassNameToToken(class) or ""
                    end
                    
                    -- 存储同步的专业数据
                    RaidCalendarCharacterDB.syncedProfessions[name] = {
                        professionCDs = professionData,
                        classToken = classToken,
                        character = name,
                        source = "guildNote"
                    }
                    syncedCount = syncedCount + 1
                end
            end
        end
    end
    
    -- 移除同步消息，保持安静运行
    if syncedCount > 0 then
        -- 刷新界面显示
        if RaidCalendar.rightPanel then
            RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
        end
    end
end

-- 筛盐器冷却逻辑
local saltShakerEventFrame = CreateFrame("Frame", "RaidCalendarSaltShakerEventFrame")
local saltShakerClickRecently = false -- 防止重复触发标记

-- 筛盐器点击事件处理（无论是否在冷却中都触发）
function RaidCalendar_OnSaltShakerClicked()
    if saltShakerClickRecently then
        return -- 防止短时间内重复触发
    end

    saltShakerClickRecently = true

    -- 设置3秒后清除标记
    local resetFrame = CreateFrame("Frame")
    local elapsed = 0
    resetFrame:SetScript("OnUpdate", function()
        elapsed = elapsed + arg1
        if elapsed >= 3 then
            resetFrame:SetScript("OnUpdate", nil)
            saltShakerClickRecently = false
        end
    end)

    -- 确保数据结构存在
    if not RaidCalendarCharacterDB then
        RaidCalendarCharacterDB = {
            professionCDs = {},
            character = UnitName("player")
        }
    end

    if not RaidCalendarCharacterDB.professionCDs then
        RaidCalendarCharacterDB.professionCDs = {}
    end

    -- 筛盐器逻辑：GC插件方式 - 直接保存冷却结束时间
    local now = time()
    local cooldownDuration = 72 * 3600 -- 筛盐器固定冷却时间：72小时（秒数）
    local endTime = now + cooldownDuration -- 计算结束时间

    RaidCalendarCharacterDB.professionCDs["制皮"] = {
        endTime = endTime             -- GC插件逻辑：冷却结束时间
    }

    -- 立即更新工会备注
    RaidCalendar_UpdateGuildNote()

    -- 立即同步自己的工会备注数据
    RaidCalendar_SyncFromGuildNotes()
    RaidCalendar_RecalculateAllCooldowns()

    -- 刷新界面显示
    if RaidCalendar.rightPanel then
        RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
    end
end

-- 注册筛盐器点击事件（监听技能使用）
saltShakerEventFrame:RegisterEvent("UNIT_SPELLCAST_SUCCEEDED")

saltShakerEventFrame:SetScript("OnEvent", function()
    if event == "UNIT_SPELLCAST_SUCCEEDED" then
        if arg1 == "player" then
            local spellName = arg2
            
            -- 只在调试模式下显示所有技能
            if RaidCalendar.debugSpells then
                DEFAULT_CHAT_FRAME:AddMessage("[调试] 检测到技能施放: " .. tostring(spellName))
            end
            
            -- 检测筛盐器技能使用（扩展匹配条件）
            if spellName and (
                string.find(spellName, "筛盐器") or 
                string.find(spellName, "Salt Shaker") or
                string.find(spellName, "精炼石中盐") or
                string.find(spellName, "盐") or
                string.find(spellName, "Salt") or
                string.find(spellName, "提炼") or
                string.find(spellName, "Refine")
            ) then
                DEFAULT_CHAT_FRAME:AddMessage("|cFF00FF00[筛盐器] 检测到筛盐器使用: " .. spellName .. "|r")
                RaidCalendar_OnSaltShakerClicked()
            end
        end
    end
end)

-- 获取筛盐器冷却时间（GC插件逻辑：基于结束时间）
function RaidCalendar_GetSaltShakerCooldown()
    if not RaidCalendarCharacterDB or not RaidCalendarCharacterDB.professionCDs or not RaidCalendarCharacterDB.professionCDs["制皮"] then
        return 0
    end

    local data = RaidCalendarCharacterDB.professionCDs["制皮"]

    -- GC插件逻辑：使用结束时间
    if data and data.endTime then
        local currentTime = time()
        local remaining = math.max(0, data.endTime - currentTime)
        return remaining
    end

    return 0
end





-- 简化的登录同步函数（移除登录上传功能）
function RaidCalendar_SyncProfessionOnLogin()
    -- 登录时自动打开显示离线成员
    SetGuildRosterShowOffline(1)
    
    local delayTimer = 0
    local syncFrame = CreateFrame("Frame")
    syncFrame:SetScript("OnUpdate", function()
        delayTimer = delayTimer + arg1
        if delayTimer >= 5.0 then
            syncFrame:SetScript("OnUpdate", nil)
            
            -- 删除：移除登录时上传冷却信息的代码
            -- RaidCalendar_SaveProfessionData()
            
            -- 2. 同步其他人的冷却信息
            RaidCalendar_SyncFromGuildNotes()
            
            -- 重新计算所有同步角色的冷却时间并更新显示
            RaidCalendar_RecalculateAllCooldowns()
            
            -- 立即刷新界面显示所有人数
            if RaidCalendar.rightPanel then
                RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
            end
            
            -- 启动技能状态监控
            RaidCalendar_StartSkillMonitoring()
            
            -- 启动工会备注变化监听
            RaidCalendar_StartGuildNoteMonitoring()
            
            -- 启动定期同步（每5分钟）
            RaidCalendar_StartPeriodicSync()
        end
    end)
end

-- 修复按钮人数统计，防止自动展开详情框，统一标题显示规则
function RaidCalendar_RefreshProfessionData(parentFrame)
    if not parentFrame or not parentFrame.professionDetails then return end
    
    local learned = RaidCalendar_AutoDetectProfessions()
    local playerName = UnitName("player")
    local _, classToken = UnitClass("player")
    local colorHex = RaidCalendar_GetClassColorHex(classToken)
    
    -- 检查是否有任何可用的专业技能（统一判断逻辑）
    local hasAnyAvailable = false
    local visibleButtonsCount = 0
    
    -- 更新每个专业
    local professions = {"炼金术", "裁缝", "制皮"}
    for _, profKey in ipairs(professions) do
        local button = parentFrame.professionButtons[profKey]
        local detailFrame = parentFrame.professionDetails[profKey]
        
        -- 检查是否应该显示这个专业（恢复原来的正确逻辑）
        local shouldShow = learned[profKey] -- 1. 自己学会了这个专业
        
        -- 2. 或者同步数据中有其他人学会了这个专业且可用
        if not shouldShow and RaidCalendarCharacterDB and RaidCalendarCharacterDB.syncedProfessions then
            for charName, charData in pairs(RaidCalendarCharacterDB.syncedProfessions) do
                if charName ~= playerName and charData.professionCDs then
                    for profession, data in pairs(charData.professionCDs) do
                        local isMatch = false
                        if profKey == "炼金术" and string.find(profession, "炼金") then
                            isMatch = true
                        elseif profKey == "裁缝" and string.find(profession, "裁缝") then
                            isMatch = true
                        elseif profKey == "制皮" and string.find(profession, "制皮") then
                            isMatch = true
                        end
                        
                        if isMatch and data and data.items and data.lastUpdate then
                            -- 关键修复：只有在确实有可用状态时才显示
                            for _, item in pairs(data.items) do
                                if item and item.remaining then
                                    local remaining = math.max(0, item.remaining - (time() - data.lastUpdate))
                                    if remaining == 0 then -- 只有可用时才显示
                                        shouldShow = true
                                        break
                                    end
                                end
                            end
                            if shouldShow then break end
                        end
                    end
                    if shouldShow then break end
                end
            end
        end
        
        -- 记录详情框的当前状态，避免意外展开
        local wasDetailVisible = detailFrame and detailFrame:IsVisible()
        
        -- 动态显示/隐藏按钮（但不影响详情框状态）
        if button then
            if shouldShow then
                button:Show()
                hasAnyAvailable = true
                visibleButtonsCount = visibleButtonsCount + 1
            else
                button:Hide()
            end
        end
        
        -- 详情框的显示状态保持不变，除非按钮被隐藏
        if detailFrame then
            if shouldShow then
                -- 只在按钮可见时保持原状态，不强制显示
                if wasDetailVisible then
                    detailFrame:Show()
                else
                    detailFrame:Hide()
                end
            else
                detailFrame:Hide()
            end
        end
        
        -- 只在按钮可见时更新内容
        if shouldShow and button then
            -- 收集角色信息（统一逻辑）
            local allCharacters = {}
            
            -- 自己的信息 - 只有在自己学会了这个专业时才添加
            if learned[profKey] then
                -- GC插件逻辑：统一使用GetSavedCooldown获取剩余时间
                local cooldown = RaidCalendar_GetSavedCooldown(profKey)
                local status = cooldown > 0 and RaidCalendar_FormatCooldownTime(cooldown) or "可"
                table.insert(allCharacters, "|cFF" .. colorHex .. playerName .. "|r - " .. status)
            end
            
            -- 修复制皮专业同步显示：同步来的角色信息
            if RaidCalendarCharacterDB and RaidCalendarCharacterDB.syncedProfessions then
                for charName, charData in pairs(RaidCalendarCharacterDB.syncedProfessions) do
                    if charName ~= playerName and charData.professionCDs then
                        for profession, data in pairs(charData.professionCDs) do
                            local shouldAdd = false
                            if profKey == "炼金术" and string.find(profession, "炼金") then
                                shouldAdd = true
                            elseif profKey == "裁缝" and string.find(profession, "裁缝") then
                                shouldAdd = true
                            elseif profKey == "制皮" and string.find(profession, "制皮") then
                                shouldAdd = true
                            end
                            
                            if shouldAdd then
                                local status = "可"
                                -- GC插件逻辑：统一使用endTime格式
                                if data and data.endTime then
                                    local remaining = math.max(0, data.endTime - time())
                                    if remaining > 0 then
                                        status = RaidCalendar_FormatCooldownTime(remaining)
                                    end
                                -- 兼容旧格式（临时）
                                elseif data and data.items and data.lastUpdate then
                                    for _, item in pairs(data.items) do
                                        if item and item.endTime then
                                            local remaining = math.max(0, item.endTime - time())
                                            if remaining > 0 then
                                                status = RaidCalendar_FormatCooldownTime(remaining)
                                                break
                                            end
                                        end
                                    end
                                end

                                local charColorHex = RaidCalendar_GetCharacterClassColor(charName)
                                table.insert(allCharacters, "|cFF" .. charColorHex .. charName .. "|r - " .. status)
                                break
                            end
                        end
                    end
                end
            end
            
            -- 更新按钮颜色（只有自己学会了才显示冷却状态）
            if button and button.profBg then
                if learned[profKey] then
                    -- GC插件逻辑：统一使用GetSavedCooldown获取剩余时间
                    local cooldown = RaidCalendar_GetSavedCooldown(profKey)

                    if cooldown > 0 then
                        button.profBg:SetTexture(0.8, 0.3, 0.3, 0.5)  -- 淡红色：有冷却
                    else
                        button.profBg:SetTexture(0.3, 0.8, 0.3, 0.5)  -- 淡绿色：可用
                    end
                else
                    -- 自己没学会，但其他人有，显示为灰色
                    button.profBg:SetTexture(0.5, 0.5, 0.5, 0.3)  -- 浅灰色：其他人有
                end
            end
            
            -- 更新按钮人数（使用统一的角色列表）
            if button then
                local professionNames = {
                    ["炼金术"] = "炼金术-转化",
                    ["裁缝"] = "裁缝-月布", 
                    ["制皮"] = "制皮-精炼"
                }
                local displayName = professionNames[profKey]
                local regions = { button:GetRegions() }
                for i, region in ipairs(regions) do
                    if region and region.SetText then
                        region:SetText(displayName .. " (" .. table.getn(allCharacters) .. "人)")
                        break
                    end
                end
            end
            
            -- 强制更新详情框内容（无论是否可见都更新）
            if detailFrame then
                -- 正确清除旧内容（修复文字重叠问题）
                local regions = { detailFrame:GetRegions() }
                for _, region in ipairs(regions) do
                    if region and region.GetText then
                        region:Hide()
                        region:SetText("")
                    end
                end
                
                -- 清除子框架
                local children = { detailFrame:GetChildren() }
                for _, child in ipairs(children) do
                    child:Hide()
                end
                
                -- 重置详情框，确保干净的状态
                detailFrame:SetHeight(18)
                
                -- 显示所有角色（使用固定的锚点避免重叠）
                for i, characterInfo in ipairs(allCharacters) do
                    local charText = detailFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
                    charText:SetPoint("TOPLEFT", detailFrame, "TOPLEFT", 25, -(i-1) * 18)  -- 从35改为30，向左移动5像素
                    charText:SetText(characterInfo)
                    charText:SetJustifyH("LEFT")
                    charText:SetWidth(150)
                end
                
                -- 根据实际角色数量调整详情框高度
                local frameHeight = math.max(18, table.getn(allCharacters) * 18)
                detailFrame:SetHeight(frameHeight)
            end
        end
    end
    
    -- 修复的标题显示/隐藏逻辑：遍历父框架的所有区域查找标题
    for _, region in ipairs({ parentFrame:GetRegions() }) do
        if region and region.GetText and region:GetText() == "专业技能冷却" then
            if hasAnyAvailable and visibleButtonsCount > 0 then
                region:Show()
            else
                region:Hide()
            end
            break
        end
    end
    
    -- 新增：刷新完成后调整右侧窗口大小
    if type(RaidCalendar_AdjustRightFrameSize) == "function" then
        RaidCalendar_AdjustRightFrameSize()
    end
end

-- 更新工会备注中的专业技能冷却信息（使用官员备注实现静默）
function RaidCalendar_UpdateGuildNote()
    local playerName = UnitName("player")
    if not playerName then
        return
    end

    -- 查找自己在工会中的位置
    local guildIndex = nil
    for i = 1, GetNumGuildMembers() do
        local name = GetGuildRosterInfo(i)
        if name == playerName then
            guildIndex = i
            break
        end
    end

    if not guildIndex then
        return -- 不在工会中
    end

    -- 检测已学专业
    local learned = RaidCalendar_AutoDetectProfessions()
    local cooldownInfo = {}

    -- 检查炼金术 - GC插件逻辑：写入冷却结束时间
    if learned["炼金术"] then
        local endTime = RaidCalendar_GetProfessionEndTime("炼金术")
        if endTime and endTime > time() then
            -- 写入结束时间戳（MMDDHHMI格式）
            local endTimeStr = date("%m%d%H%M", endTime)
            table.insert(cooldownInfo, "转" .. endTimeStr)
        else
            table.insert(cooldownInfo, "转可")
        end
    end

    -- 检查裁缝 - GC插件逻辑：写入冷却结束时间
    if learned["裁缝"] then
        local endTime = RaidCalendar_GetProfessionEndTime("裁缝")
        if endTime and endTime > time() then
            -- 写入结束时间戳（MMDDHHMI格式）
            local endTimeStr = date("%m%d%H%M", endTime)
            table.insert(cooldownInfo, "月" .. endTimeStr)
        else
            table.insert(cooldownInfo, "月可")
        end
    end

    -- 筛盐器上传逻辑 - GC插件逻辑：写入冷却结束时间
    if learned["制皮"] then
        local data = RaidCalendarCharacterDB.professionCDs and RaidCalendarCharacterDB.professionCDs["制皮"]
        if data and data.endTime then
            if data.endTime > time() then
                local endTimeStr = date("%m%d%H%M", data.endTime)
                table.insert(cooldownInfo, "盐" .. endTimeStr)
            else
                table.insert(cooldownInfo, "盐可")
            end
        else
            table.insert(cooldownInfo, "盐可")
        end
    end

    -- 构建备注内容
    local noteContent = ""
    if table.getn(cooldownInfo) > 0 then
        noteContent = table.concat(cooldownInfo, ",")
        if string.len(noteContent) > 31 then
            noteContent = string.sub(noteContent, 1, 30) .. "+"
        end
    end

    -- 获取当前官员备注（静默模式）
    local _, _, _, _, _, _, _, officerNote = GetGuildRosterInfo(guildIndex)

    -- 直接更新官员备注，实现静默运行
    if noteContent ~= officerNote then
        GuildRosterSetOfficerNote(guildIndex, noteContent)

        -- 延迟同步和刷新界面，确保工会备注更新生效（使用经典版本兼容的定时器）
        local delayFrame = CreateFrame("Frame")
        local delayTimer = 0
        delayFrame:SetScript("OnUpdate", function()
            delayTimer = delayTimer + arg1
            if delayTimer >= 0.5 then
                delayFrame:SetScript("OnUpdate", nil)
                RaidCalendar_SyncFromGuildNotes()
                RaidCalendar_RecalculateAllCooldowns()

                -- 刷新界面显示
                if RaidCalendar.rightPanel then
                    RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
                end
            end
        end)
    end
end

-- 解析工会备注格式（GC插件逻辑：解析结束时间）
function RaidCalendar_ParseGuildNoteData(cooldownStr, noteTime)
    if not cooldownStr or cooldownStr == "" then return nil end

    local professionData = {}
    local currentTime = time()

    -- 解析格式：转06120208,月06120208,盐06120208 或 转可,月可,盐可
    local items = {}
    local start = 1
    while true do
        local pos = string.find(cooldownStr, ",", start, true)
        if not pos then
            table.insert(items, string.sub(cooldownStr, start))
            break
        end
        table.insert(items, string.sub(cooldownStr, start, pos - 1))
        start = pos + 1
    end

    for _, item in ipairs(items) do
        local skillName = ""
        local endTimeStr = ""
        local isAvailable = false

        -- 识别技能类型和状态（GC插件格式）
        if string.find(item, "月可") then
            skillName = "月布"
            isAvailable = true
        elseif string.find(item, "转可") then
            skillName = "转化"
            isAvailable = true
        elseif string.find(item, "盐可") then
            skillName = "筛盐器"
            isAvailable = true
        elseif string.find(item, "^月") then
            skillName = "月布"
            endTimeStr = string.sub(item, 2) -- 去掉"月"字符
        elseif string.find(item, "^转") then
            skillName = "转化"
            endTimeStr = string.sub(item, 2) -- 去掉"转"字符
        elseif string.find(item, "^盐") then
            skillName = "筛盐器"
            endTimeStr = string.sub(item, 2) -- 去掉"盐"字符
        end

        if skillName ~= "" then
            local endTime = nil
            local remainingSeconds = 0

            if isAvailable then
                remainingSeconds = 0 -- 可用状态
                endTime = currentTime -- 已经结束
            elseif endTimeStr ~= "" and string.len(endTimeStr) == 8 then
                -- GC插件逻辑：解析结束时间戳（MMDDHHMI格式）
                endTime = RaidCalendar_ParseTimeStamp(endTimeStr)
                remainingSeconds = math.max(0, endTime - currentTime)
            end

            -- 根据技能名称确定专业
            local profession = ""
            local fullSkillName = ""

            if skillName == "月布" then
                profession = "裁缝"
                fullSkillName = "月布"
            elseif skillName == "转化" then
                profession = "炼金术"
                fullSkillName = "转化：奥金"
            elseif skillName == "筛盐器" then
                profession = "制皮"
                fullSkillName = "筛盐器"
            end

            if profession ~= "" then
                -- GC插件逻辑：直接保存结束时间，不使用items数组
                professionData[profession] = {
                    endTime = endTime or currentTime
                }
            end
        end
    end

    return next(professionData) and professionData or nil
end

-- 修复：解析简化数字时间格式（只处理4位数字）
function RaidCalendar_ParseSimpleTimeString(timeStr)
    if not timeStr or timeStr == "" then return 0 end
    
    -- 移除可能的 + 号（截断标记）
    timeStr = string.gsub(timeStr, "+", "")
    
    -- 过滤出纯数字字符，忽略其他字符
    local numericStr = ""
    for i = 1, string.len(timeStr) do
        local char = string.sub(timeStr, i, i)
        local ascii = string.byte(char)
        
        -- 只保留ASCII码48-57的数字字符
        if ascii >= 48 and ascii <= 57 then
            numericStr = numericStr .. char
        end
    end
    
    if numericStr == "" or string.len(numericStr) ~= 4 then
        return 0
    end
    
    -- 4位数字 = HHMM格式（前2位小时，后2位分钟）
    local hours = tonumber(string.sub(numericStr, 1, 2))
    local minutes = tonumber(string.sub(numericStr, 3, 4))
    
    if hours and minutes and hours <= 99 and minutes <= 59 then
        return hours * 3600 + minutes * 60
    end
    
    return 0
end

-- 获取当前时间戳（秒）
function RaidCalendar_GetCurrentTimestamp()
    return time()
end

-- 修复解析时间戳（MMDDHHMI格式）转为时间戳 - 正确处理跨月
function RaidCalendar_ParseTimeStamp(timeStampStr)
    if not timeStampStr or string.len(timeStampStr) ~= 8 then
        return time() -- 如果格式不对，返回当前时间
    end
    
    local month = tonumber(string.sub(timeStampStr, 1, 2))
    local day = tonumber(string.sub(timeStampStr, 3, 4))
    local hour = tonumber(string.sub(timeStampStr, 5, 6))
    local minute = tonumber(string.sub(timeStampStr, 7, 8))
    
    -- 获取当前时间信息
    local currentTime = time()
    local currentYear = tonumber(date("%Y", currentTime))
    local currentMonth = tonumber(date("%m", currentTime))
    
    -- 构造目标时间，假设是今年
    local targetTime = time({
        year = currentYear,
        month = month,
        day = day,
        hour = hour,
        min = minute,
        sec = 0
    })
    
    -- 如果目标时间比当前时间晚超过15天，说明是上个月的时间
    if targetTime - currentTime > 15 * 24 * 3600 then
        -- 如果当前是1月，上个月是去年12月
        if currentMonth == 1 then
            targetTime = time({
                year = currentYear - 1,
                month = 12,
                day = day,
                hour = hour,
                min = minute,
                sec = 0
            })
        else
            -- 正常情况，上个月
            targetTime = time({
                year = currentYear,
                month = month,
                day = day,
                hour = hour,
                min = minute,
                sec = 0
            })
        end
    -- 如果目标时间比当前时间早超过15天，说明是下个月的时间
    elseif currentTime - targetTime > 15 * 24 * 3600 then
        -- 如果当前是12月，下个月是明年1月
        if currentMonth == 12 then
            targetTime = time({
                year = currentYear + 1,
                month = 1,
                day = day,
                hour = hour,
                min = minute,
                sec = 0
            })
        else
            -- 正常情况，下个月
            targetTime = time({
                year = currentYear,
                month = month,
                day = day,
                hour = hour,
                min = minute,
                sec = 0
            })
        end
    end
    
    return targetTime
end



-- 简化登录事件监听（移除调试信息）
local loginFrame = CreateFrame("Frame", "RaidCalendarProfessionLoginFrame")
loginFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
loginFrame:SetScript("OnEvent", function()
    if event == "PLAYER_ENTERING_WORLD" then
        -- 延迟0.5秒确保聊天框准备好
        local delayFrame = CreateFrame("Frame")
        local delayTimer = 0
        delayFrame:SetScript("OnUpdate", function()
            delayTimer = delayTimer + arg1
            if delayTimer >= 0.5 then
                delayFrame:SetScript("OnUpdate", nil)
                RaidCalendar_SyncProfessionOnLogin()
            end        end)
        loginFrame:UnregisterEvent("PLAYER_ENTERING_WORLD")
    end
end)

-- 监听专业技能窗口打开事件（仿照GC插件逻辑）
local professionFrame = CreateFrame("Frame")
professionFrame:RegisterEvent("TRADE_SKILL_SHOW")
professionFrame:RegisterEvent("TRADE_SKILL_CLOSE")
professionFrame:RegisterEvent("TRADE_SKILL_UPDATE")  -- GC插件逻辑：监听专业技能更新
professionFrame:RegisterEvent("BAG_UPDATE_COOLDOWN") -- GC插件逻辑：监听物品冷却更新
professionFrame:RegisterEvent("BAG_UPDATE")          -- GC插件逻辑：监听背包更新
professionFrame:SetScript("OnEvent", function()
    if event == "TRADE_SKILL_SHOW" then
        -- GC插件逻辑：专业技能面板打开时检测冷却
        RaidCalendar_UpdateCurrentTradeskillCooldown()
        -- GC插件逻辑：事件驱动，无需持续监听
    elseif event == "TRADE_SKILL_CLOSE" then
        -- GC插件逻辑：事件驱动，无需停止监听
    elseif event == "TRADE_SKILL_UPDATE" then
        -- GC插件逻辑：专业技能面板更新时检测冷却
        RaidCalendar_UpdateCurrentTradeskillCooldown()
    elseif event == "BAG_UPDATE_COOLDOWN" or event == "BAG_UPDATE" then
        -- GC插件逻辑：背包更新时检测物品冷却
        RaidCalendar_CheckItemCooldowns()
    end
end)



-- 新增：技能可用状态通知系统
RaidCalendar.skillNotifications = {
    lastNotificationTime = {},  -- 记录每个技能的最后通知时间
    notificationInterval = 600, -- 10分钟间隔（600秒）
    checkInterval = 600,        -- 10分钟检查间隔
    availableSkills = {},       -- 记录当前可用的技能
    monitoringStarted = false
}

-- 新增：检查技能状态并发送通知
function RaidCalendar_CheckAndNotifyAvailableSkills()
    if not IsInGuild() then return end
    
    local currentTime = time()
    local notifications = RaidCalendar.skillNotifications
    local currentAvailableSkills = {}
    
    -- 专业技能名称映射
    local skillNames = {
        ["炼金术"] = "转化技能",
        ["裁缝"] = "月布技能", 
        ["制皮"] = "精炼技能"
    }
    
    -- 检查自己的技能状态
    local playerName = UnitName("player")
    local learned = RaidCalendar_AutoDetectProfessions()
    
    for profKey, skillName in pairs(skillNames) do
        if learned[profKey] then
            -- 跳过制皮专业的自动检测，避免频繁调试信息
            if profKey ~= "制皮" then
                local cooldown = RaidCalendar_GetSavedCooldown(profKey)
                if cooldown <= 0 then
                    local skillKey = playerName .. "_" .. profKey
                    currentAvailableSkills[skillKey] = {
                        charName = playerName,
                        skillName = skillName
                    }
                end
            end
        end
    end
    
    -- 检查同步角色的技能状态
    if RaidCalendarCharacterDB and RaidCalendarCharacterDB.syncedProfessions then
        for charName, charData in pairs(RaidCalendarCharacterDB.syncedProfessions) do
            if charName ~= playerName and charData.professionCDs then
                for profession, data in pairs(charData.professionCDs) do
                    local profKey = ""
                    local skillName = ""
                    
                    if string.find(profession, "炼金") then
                        profKey = "炼金术"
                        skillName = "转化技能"
                    elseif string.find(profession, "裁缝") then
                        profKey = "裁缝"
                        skillName = "月布技能"
                    elseif string.find(profession, "制皮") then
                        profKey = "制皮"
                        skillName = "精炼技能"
                    end
                    
                    if profKey ~= "" and data and data.items and data.lastUpdate then
                        local isAvailable = false
                        for _, item in pairs(data.items) do
                            if item and item.remaining then
                                local remaining = math.max(0, item.remaining - (currentTime - data.lastUpdate))
                                if remaining <= 0 then
                                    isAvailable = true
                                    break
                                end
                            end
                        end
                        
                        if isAvailable then
                            local skillKey = charName .. "_" .. profKey
                            currentAvailableSkills[skillKey] = {
                                charName = charName,
                                skillName = skillName
                            }
                        end
                    end
                end
            end
        end
    end
    
    -- 处理所有当前可用的技能
    for skillKey, skillInfo in pairs(currentAvailableSkills) do
        local lastNotifyTime = notifications.lastNotificationTime[skillKey] or 0
        local shouldNotify = false
        
        -- 检查是否是新变为可用的技能（首次发现可用）
        if not notifications.availableSkills[skillKey] then
            shouldNotify = true -- 刚变为可用，立即发送
        else
            -- 已经是可用状态，检查是否到了10分钟间隔
            if currentTime - lastNotifyTime >= notifications.notificationInterval then
                shouldNotify = true
            end
        end
        
        if shouldNotify then
            RaidCalendar_SendSkillAvailableNotification(skillInfo.charName, skillInfo.skillName)
            notifications.lastNotificationTime[skillKey] = currentTime
        end
    end
    
    -- 更新可用技能记录
    notifications.availableSkills = currentAvailableSkills
end

-- 新增：发送技能可用通知
function RaidCalendar_SendSkillAvailableNotification(charName, skillName)
    -- 屏幕中央绿色字体提示（30秒显示）
    local message = charName .. "的" .. skillName .. "已经可用"
    UIErrorsFrame:AddMessage(message, 0.0, 1.0, 0.0, 1.0, 30.0)
    
    -- 声音提示
    PlaySound("AuctionWindowClose")
end

-- 新增：启动技能状态监控
function RaidCalendar_StartSkillMonitoring()
    if RaidCalendar.skillNotifications.monitoringStarted then
        return -- 已经启动
    end
    
    RaidCalendar.skillNotifications.monitoringStarted = true
    
    local monitorFrame = CreateFrame("Frame", "RaidCalendarSkillNotificationMonitor")
    local elapsed = 0
    
    monitorFrame:SetScript("OnUpdate", function()
        elapsed = elapsed + arg1
        
        if elapsed >= RaidCalendar.skillNotifications.checkInterval then
            elapsed = 0
            RaidCalendar_CheckAndNotifyAvailableSkills()
        end
    end)
    
    RaidCalendar.skillNotificationMonitorFrame = monitorFrame
    
    -- 启动后立即执行一次检查
    RaidCalendar_CheckAndNotifyAvailableSkills()
end

-- 新增：停止技能状态监控
function RaidCalendar_StopSkillMonitoring()
    if RaidCalendar.skillNotificationMonitorFrame then
        RaidCalendar.skillNotificationMonitorFrame:SetScript("OnUpdate", nil)
        RaidCalendar.skillNotificationMonitorFrame = nil
    end
    RaidCalendar.skillNotifications.monitoringStarted = false
end

-- 新增：重新计算所有同步角色的冷却时间（排除自己）
function RaidCalendar_RecalculateAllCooldowns()
    if not RaidCalendarCharacterDB or not RaidCalendarCharacterDB.syncedProfessions then
        return
    end
    
    local currentTime = time()
    local updateCount = 0
    local currentPlayerName = UnitName("player")
    
    -- 遍历所有同步的角色数据（排除自己）
    for charName, charData in pairs(RaidCalendarCharacterDB.syncedProfessions) do
        if charName ~= currentPlayerName and charData.professionCDs then
            for profession, data in pairs(charData.professionCDs) do
               
                if data and data.items and data.lastUpdate then
                    -- GC插件逻辑：基于结束时间重新计算剩余冷却时间
                    for _, item in pairs(data.items) do
                        -- GC插件逻辑：使用结束时间计算
                        if item and item.endTime then
                            local oldRemaining = item.remaining or 0
                            local newRemaining = math.max(0, item.endTime - currentTime)

                            -- 更新remaining字段以保持显示兼容性
                            if newRemaining ~= oldRemaining then
                                item.remaining = newRemaining
                                updateCount = updateCount + 1
                            end
                        end
                    end
                    -- 更新最后计算时间为当前时间
                    data.lastUpdate = currentTime
                end
            end
        end
    end
    
    if updateCount > 0 then
        -- 立即刷新界面显示更新后的数据
        if RaidCalendar.rightPanel then
            RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
        end
    end
end

-- 新增：启动工会备注变化监听
function RaidCalendar_StartGuildNoteMonitoring()
    if RaidCalendar.guildNoteMonitorStarted then
        return -- 已经启动
    end
    
    RaidCalendar.guildNoteMonitorStarted = true
    
    -- 创建工会备注监听框架
    local guildFrame = CreateFrame("Frame", "RaidCalendarGuildNoteMonitor")
    guildFrame:RegisterEvent("GUILD_ROSTER_UPDATE")
    
    -- 保存当前所有成员的官员备注
    local lastOfficerNotes = {}
    
    -- 初始化备注缓存
    local function updateNoteCache()
        for i = 1, GetNumGuildMembers() do
            local name, _, _, _, _, _, _, officerNote = GetGuildRosterInfo(i)
            if name then
                lastOfficerNotes[name] = officerNote or ""
            end
        end
    end
    
    guildFrame:SetScript("OnEvent", function()
        if event == "GUILD_ROSTER_UPDATE" then
            -- 延迟1秒处理，确保数据更新完成
            local delayFrame = CreateFrame("Frame")
            local delayTimer = 0
            delayFrame:SetScript("OnUpdate", function()
                delayTimer = delayTimer + arg1
                if delayTimer >= 1.0 then
                    delayFrame:SetScript("OnUpdate", nil)
                    
                    local hasChanges = false
                    
                    -- 检查是否有官员备注变化
                    for i = 1, GetNumGuildMembers() do
                        local name, _, _, _, _, _, _, officerNote = GetGuildRosterInfo(i)
                        if name then
                            local currentNote = officerNote or ""
                            local lastNote = lastOfficerNotes[name] or ""
                            
                            -- 检查是否有专业技能相关的备注变化
                            if currentNote ~= lastNote and 
                               (string.find(currentNote, "月") or string.find(currentNote, "转") or string.find(currentNote, "盐")) then
                                hasChanges = true
                                lastOfficerNotes[name] = currentNote
                            end
                        end
                    end
                      -- 如果检测到变化，立即同步
                    if hasChanges then
                        RaidCalendar_SyncFromGuildNotes()
                        RaidCalendar_RecalculateAllCooldowns()
                        
                        if RaidCalendar.rightPanel then
                            RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
                        end
                    end
                end
            end)
        end
    end)
    
    -- 初始化缓存
    updateNoteCache()
    
    RaidCalendar.guildNoteMonitorFrame = guildFrame
end

-- 新增：启动定期同步（每5分钟）
function RaidCalendar_StartPeriodicSync()
    if RaidCalendar.periodicSyncStarted then
        return -- 已经启动
    end
    
    RaidCalendar.periodicSyncStarted = true
    
    local syncFrame = CreateFrame("Frame", "RaidCalendarPeriodicSync")
    local elapsed = 0
    local syncInterval = 300 -- 每5分钟（300秒）
    
    syncFrame:SetScript("OnUpdate", function()
        elapsed = elapsed + arg1

        if elapsed >= syncInterval then
            elapsed = 0
            
            -- 执行定期同步
            
            -- 同步其他人的冷却信息
            RaidCalendar_SyncFromGuildNotes()
            
            -- 重新计算冷却时间
            RaidCalendar_RecalculateAllCooldowns()
            
            -- 刷新界面显示
            if RaidCalendar.rightPanel then
                RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
            end
            
            -- 检查并通知可用技能
            RaidCalendar_CheckAndNotifyAvailableSkills()
        end
    end)
    
    RaidCalendar.periodicSyncFrame = syncFrame
end

-- 新增：停止工会备注监听
function RaidCalendar_StopGuildNoteMonitoring()
    if RaidCalendar.guildNoteMonitorFrame then
        RaidCalendar.guildNoteMonitorFrame:UnregisterEvent("GUILD_ROSTER_UPDATE")
        RaidCalendar.guildNoteMonitorFrame:SetScript("OnEvent", nil)
        RaidCalendar.guildNoteMonitorFrame = nil
    end
    RaidCalendar.guildNoteMonitorStarted = false
end

-- 新增：停止定期同步
function RaidCalendar_StopPeriodicSync()
    if RaidCalendar.periodicSyncFrame then
        RaidCalendar.periodicSyncFrame:SetScript("OnUpdate", nil)
        RaidCalendar.periodicSyncFrame = nil
    end
    RaidCalendar.periodicSyncStarted = false
end

